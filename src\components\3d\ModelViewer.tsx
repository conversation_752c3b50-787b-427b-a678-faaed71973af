'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Shi<PERSON>, User, Spark<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON>, Heart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationAPI } from '@/lib/api';
import { ColorRecommendation } from '@/types';
import { MaleModel } from './models/MaleModel';
import { FemaleModel } from './models/FemaleModel';
import { FemaleCasualModel } from './models/FemaleCasualModel';
import { FemaleFormalModel } from './models/FemaleFormalModel';
import { FemaleCheongsam } from './models/FemaleCheongsam';
import { toast } from 'react-hot-toast';

interface ModelViewerProps {
  analysisId?: string;
}

type ModelType = 'male' | 'female-casual' | 'female-formal' | 'female-cheongsam';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({ analysisId }) => {
  const { user } = useAuth();
  const [selectedModel, setSelectedModel] = useState<ModelType>('male');
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [selectedOutfitIndex, setSelectedOutfitIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [colorCombination, setColorCombination] = useState<ColorCombination | null>(null);

  // Set default model based on user gender
  useEffect(() => {
    if (user?.gender === 'female') {
      setSelectedModel('female-casual');
    } else {
      setSelectedModel('male');
    }
  }, [user?.gender]);

  // Fetch latest recommendations on component mount
  useEffect(() => {
    fetchLatestRecommendations();
  }, []);

  // Update color combination when recommendations or outfit selection changes
  useEffect(() => {
    if (recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const outfit = recommendations.outfits[selectedOutfitIndex];
      setColorCombination({
        shirt: outfit.shirt.hex,
        pants: outfit.pants.hex,
        shoes: outfit.shoes?.hex || '#8B4513'
      });
    }
  }, [recommendations, selectedOutfitIndex]);

  const fetchLatestRecommendations = async () => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getLatestRecommendation();
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Latest recommendations loaded:', response.data);
      } else {
        console.log('ModelViewer: No recommendations found');
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecommendationsForAnalysis = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getRecommendations(id, {
        style: 'casual',
        occasion: 'everyday'
      });
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Analysis recommendations loaded:', response.data);
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch analysis recommendations:', error);
      toast.error('Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelChange = (modelType: ModelType) => {
    setSelectedModel(modelType);
    console.log('ModelViewer: Model changed to:', modelType);
  };

  const handleOutfitChange = (index: number) => {
    setSelectedOutfitIndex(index);
    console.log('ModelViewer: Outfit changed to index:', index);
  };

  const renderModel = () => {
    const modelProps = {
      colorCombination,
      enableColorSync: !!colorCombination
    };

    switch (selectedModel) {
      case 'male':
        return <MaleModel {...modelProps} />;
      case 'female-casual':
        return <FemaleCasualModel {...modelProps} />;
      case 'female-formal':
        return <FemaleFormalModel {...modelProps} />;
      case 'female-cheongsam':
        return <FemaleCheongsam {...modelProps} />;
      default:
        return <MaleModel {...modelProps} />;
    }
  };

  const getModelOptions = () => {
    if (user?.gender === 'female') {
      return [
        {
          value: 'female-casual',
          label: 'Casual Wear',
          icon: Shirt,
          description: 'Everyday comfort style',
          gradient: 'from-pink-500 to-rose-500'
        },
        {
          value: 'female-formal',
          label: 'Formal Wear',
          icon: User,
          description: 'Professional elegance',
          gradient: 'from-purple-500 to-indigo-500'
        },
        {
          value: 'female-cheongsam',
          label: 'One Piece',
          icon: Sparkles,
          description: 'Traditional beauty',
          gradient: 'from-emerald-500 to-teal-500'
        }
      ];
    } else {
      return [
        {
          value: 'male',
          label: 'Male Model',
          icon: User,
          description: 'Classic masculine style',
          gradient: 'from-blue-500 to-cyan-500'
        }
      ];
    }
  };

  return (
    <div className="w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start gap-3 mb-2">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                  <Eye className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                  3D Style Studio
                </h1>
              </div>
              <p className="text-purple-200 text-sm lg:text-base">
                {user?.gender === 'female' ? '✨ Explore Your Style Universe' : '🚀 Discover Your Look'} •
                Interactive color magic awaits
              </p>
            </div>

            {/* Model Selection */}
            <div className="flex flex-wrap justify-center lg:justify-end gap-3">
              {getModelOptions().map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    onClick={() => handleModelChange(option.value as ModelType)}
                    className={`group relative overflow-hidden px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 ${
                      selectedModel === option.value
                        ? `bg-gradient-to-r ${option.gradient} text-white shadow-2xl shadow-purple-500/25`
                        : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <IconComponent className="w-5 h-5" />
                      <div className="text-left">
                        <div className="text-sm font-semibold">{option.label}</div>
                        <div className="text-xs opacity-80">{option.description}</div>
                      </div>
                    </div>
                    {selectedModel === option.value && (
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-50"></div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Current Color Combination Display */}
      {colorCombination && (
        <div className="absolute top-32 right-6 z-10 bg-white/15 backdrop-blur-xl rounded-3xl p-6 shadow-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Palette className="w-4 h-4 text-white" />
            </div>
            <h4 className="text-sm font-bold text-white">Active Palette</h4>
          </div>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.shirt }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"></div>
              </div>
              <span className="text-sm text-white font-medium">Shirt</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.pants }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse animation-delay-1000"></div>
              </div>
              <span className="text-sm text-white font-medium">Pants</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.shoes }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full animate-pulse animation-delay-2000"></div>
              </div>
              <span className="text-sm text-white font-medium">Shoes</span>
            </div>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ height: '100vh' }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <Environment preset="studio" />

        {renderModel()}

        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={10}
        />
      </Canvas>

      {/* No Recommendations Message */}
      {!recommendations && !isLoading && (
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/50 to-transparent backdrop-blur-xl">
          <div className="max-w-4xl mx-auto px-6 py-12 text-center">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-20 h-20 mx-auto flex items-center justify-center">
                  <Sparkles className="w-10 h-10 text-white animate-spin" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-3 bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                Discover Your Perfect Colors
              </h3>
              <p className="text-purple-200 mb-6 max-w-md mx-auto leading-relaxed">
                Upload and analyze a face photo to unlock personalized color magic for your 3D avatar.
                Let AI discover your perfect palette!
              </p>
              <button
                onClick={fetchLatestRecommendations}
                className="group relative px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl font-semibold shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center gap-3">
                  <RefreshCw className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500" />
                  <span>Discover My Colors</span>
                  <Star className="w-5 h-5 animate-pulse" />
                </div>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Color Recommendations Panel */}
      {recommendations && recommendations.outfits && (
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/60 via-black/40 to-transparent backdrop-blur-2xl">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">
                      AI Color Recommendations
                    </h3>
                    <p className="text-purple-200 text-sm">
                      {recommendations.outfits.length} personalized outfit combinations
                    </p>
                  </div>
                </div>
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl px-3 py-2 border border-white/20">
                    <span className="text-purple-200">AI: </span>
                    <span className="text-white font-medium">{recommendations.aiService}</span>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl px-3 py-2 border border-white/20">
                    <span className="text-purple-200">Speed: </span>
                    <span className="text-white font-medium">{recommendations.processingTime}ms</span>
                  </div>
                  <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-xl px-3 py-2 border border-green-400/30">
                    <span className="text-green-200">Confidence: </span>
                    <span className="text-green-100 font-bold">{Math.round(recommendations.confidence * 100)}%</span>
                  </div>
                </div>
              </div>
            
            {/* Outfit Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendations.outfits.map((outfit, index) => (
                <button
                  key={index}
                  onClick={() => handleOutfitChange(index)}
                  className={`group relative overflow-hidden p-6 rounded-3xl transition-all duration-500 transform hover:scale-105 ${
                    selectedOutfitIndex === index
                      ? 'bg-gradient-to-br from-purple-500/30 to-pink-500/30 border-2 border-purple-400/50 shadow-2xl shadow-purple-500/25'
                      : 'bg-white/10 border-2 border-white/20 hover:bg-white/20 hover:border-white/30'
                  }`}
                >
                  {/* Animated Background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative text-left">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-bold text-white text-lg">{outfit.outfitName}</h4>
                      {selectedOutfitIndex === index && (
                        <div className="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 px-3 py-1 rounded-full">
                          <Heart className="w-3 h-3 text-white animate-pulse" />
                          <span className="text-xs text-white font-bold">Active</span>
                        </div>
                      )}
                    </div>

                    {/* Color Swatches with Labels */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center space-x-3 group/item">
                        <div className="relative">
                          <div
                            className="w-10 h-10 rounded-2xl border-2 border-white/30 shadow-xl group-hover/item:scale-110 transition-transform duration-300"
                            style={{ backgroundColor: outfit.shirt.hex }}
                          />
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                            <Shirt className="w-2 h-2 text-white" />
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-white font-semibold">{outfit.shirt.color}</div>
                          <div className="text-xs text-purple-200">Shirt</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3 group/item">
                        <div className="relative">
                          <div
                            className="w-10 h-10 rounded-2xl border-2 border-white/30 shadow-xl group-hover/item:scale-110 transition-transform duration-300"
                            style={{ backgroundColor: outfit.pants.hex }}
                          />
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full flex items-center justify-center">
                            <User className="w-2 h-2 text-white" />
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-white font-semibold">{outfit.pants.color}</div>
                          <div className="text-xs text-purple-200">Pants</div>
                        </div>
                      </div>

                      {outfit.shoes && (
                        <div className="flex items-center space-x-3 group/item">
                          <div className="relative">
                            <div
                              className="w-10 h-10 rounded-2xl border-2 border-white/30 shadow-xl group-hover/item:scale-110 transition-transform duration-300"
                              style={{ backgroundColor: outfit.shoes.hex }}
                            />
                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full flex items-center justify-center">
                              <Sparkles className="w-2 h-2 text-white" />
                            </div>
                          </div>
                          <div>
                            <div className="text-sm text-white font-semibold">{outfit.shoes.color}</div>
                            <div className="text-xs text-purple-200">Shoes</div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-3 border border-white/20">
                      <p className="text-sm text-purple-100 leading-relaxed">{outfit.overallReason}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Additional Color Palette Information */}
            {recommendations.colorPalette && (
              <div className="mt-8 pt-6 border-t border-white/20">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-3xl p-6 border border-green-400/30">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl">
                        <Star className="w-5 h-5 text-white" />
                      </div>
                      <h4 className="text-lg font-bold text-white">Perfect Colors</h4>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {recommendations.colorPalette.bestColors.map((color, index) => (
                        <div
                          key={index}
                          className="group relative"
                          title={color}
                        >
                          <div
                            className="w-12 h-12 rounded-2xl border-2 border-white/30 shadow-xl hover:scale-110 transition-transform duration-300 cursor-pointer"
                            style={{ backgroundColor: color }}
                          />
                          <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Heart className="w-3 h-3 text-white" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-red-500/20 to-orange-500/20 backdrop-blur-sm rounded-3xl p-6 border border-red-400/30">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl">
                        <Eye className="w-5 h-5 text-white" />
                      </div>
                      <h4 className="text-lg font-bold text-white">Avoid These</h4>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {recommendations.colorPalette.avoidColors.map((color, index) => (
                        <div
                          key={index}
                          className="group relative"
                          title={color}
                        >
                          <div
                            className="w-12 h-12 rounded-2xl border-2 border-white/20 shadow-xl opacity-60 hover:opacity-80 transition-all duration-300 cursor-pointer"
                            style={{ backgroundColor: color }}
                          />
                          <div className="absolute inset-0 bg-red-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* AI Advice */}
                {recommendations.advice && (
                  <div className="mt-6 bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-3xl p-6 border border-purple-400/30">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                        <Sparkles className="w-5 h-5 text-white" />
                      </div>
                      <h4 className="text-lg font-bold text-white">AI Styling Wisdom</h4>
                    </div>
                    <p className="text-purple-100 leading-relaxed">{recommendations.advice}</p>
                  </div>
                )}

                {/* Seasonal Type */}
                {recommendations.colorPalette.seasonalType && (
                  <div className="mt-6 text-center">
                    <div className="inline-flex items-center gap-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl px-6 py-3 border border-yellow-400/30">
                      <Star className="w-5 h-5 text-yellow-300 animate-pulse" />
                      <span className="text-white font-bold text-lg">
                        Your Color Season: {recommendations.colorPalette.seasonalType}
                      </span>
                      <Star className="w-5 h-5 text-yellow-300 animate-pulse" />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/60 backdrop-blur-xl flex items-center justify-center z-20">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20 text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
                <div className="absolute inset-2 border-4 border-transparent border-t-pink-500 rounded-full animate-spin animation-delay-150"></div>
                <div className="absolute inset-4 border-4 border-transparent border-t-blue-500 rounded-full animate-spin animation-delay-300"></div>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-bold text-white">Analyzing Colors</h3>
              <p className="text-purple-200">AI is crafting your perfect palette...</p>
              <div className="flex justify-center space-x-1 mt-4">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce animation-delay-100"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
