'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Shirt, User, Sparkles } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationAPI } from '@/lib/api';
import { ColorRecommendation } from '@/types';
import { MaleModel } from './models/MaleModel';
import { FemaleModel } from './models/FemaleModel';
import { FemaleCasualModel } from './models/FemaleCasualModel';
import { FemaleFormalModel } from './models/FemaleFormalModel';
import { FemaleCheongsam } from './models/FemaleCheongsam';
import { toast } from 'react-hot-toast';

interface ModelViewerProps {
  analysisId?: string;
}

type ModelType = 'male' | 'female-casual' | 'female-formal' | 'female-cheongsam';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({ analysisId }) => {
  const { user } = useAuth();
  const [selectedModel, setSelectedModel] = useState<ModelType>('male');
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [selectedOutfitIndex, setSelectedOutfitIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [colorCombination, setColorCombination] = useState<ColorCombination | null>(null);

  // Set default model based on user gender
  useEffect(() => {
    if (user?.gender === 'female') {
      setSelectedModel('female-casual');
    } else {
      setSelectedModel('male');
    }
  }, [user?.gender]);

  // Fetch latest recommendations on component mount
  useEffect(() => {
    fetchLatestRecommendations();
  }, []);

  // Update color combination when recommendations or outfit selection changes
  useEffect(() => {
    if (recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const outfit = recommendations.outfits[selectedOutfitIndex];
      setColorCombination({
        shirt: outfit.shirt.hex,
        pants: outfit.pants.hex,
        shoes: outfit.shoes?.hex || '#8B4513'
      });
    }
  }, [recommendations, selectedOutfitIndex]);

  const fetchLatestRecommendations = async () => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getLatestRecommendation();
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Latest recommendations loaded:', response.data);
      } else {
        console.log('ModelViewer: No recommendations found');
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecommendationsForAnalysis = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getRecommendations(id, {
        style: 'casual',
        occasion: 'everyday'
      });
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Analysis recommendations loaded:', response.data);
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch analysis recommendations:', error);
      toast.error('Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelChange = (modelType: ModelType) => {
    setSelectedModel(modelType);
    console.log('ModelViewer: Model changed to:', modelType);
  };

  const handleOutfitChange = (index: number) => {
    setSelectedOutfitIndex(index);
    console.log('ModelViewer: Outfit changed to index:', index);
  };

  const renderModel = () => {
    const modelProps = {
      colorCombination,
      enableColorSync: !!colorCombination
    };

    switch (selectedModel) {
      case 'male':
        return <MaleModel {...modelProps} />;
      case 'female-casual':
        return <FemaleCasualModel {...modelProps} />;
      case 'female-formal':
        return <FemaleFormalModel {...modelProps} />;
      case 'female-cheongsam':
        return <FemaleCheongsam {...modelProps} />;
      default:
        return <MaleModel {...modelProps} />;
    }
  };

  const getModelOptions = () => {
    if (user?.gender === 'female') {
      return [
        { value: 'female-casual', label: 'Casual Wear', icon: Shirt },
        { value: 'female-formal', label: 'Formal Wear', icon: User },
        { value: 'female-cheongsam', label: 'One Piece', icon: Sparkles }
      ];
    } else {
      return [
        { value: 'male', label: 'Male Model', icon: User }
      ];
    }
  };

  return (
    <div className="w-full h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">3D Model Viewer</h1>
              <p className="text-sm text-gray-600 mt-1">
                {user?.gender === 'female' ? 'Female Models' : 'Male Model'} •
                Click on color combinations below to apply them to the model
              </p>
            </div>

            {/* Model Selection */}
            <div className="flex items-center space-x-2">
              {getModelOptions().map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    onClick={() => handleModelChange(option.value as ModelType)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                      selectedModel === option.value
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                    }`}
                  >
                    <IconComponent className="w-4 h-4" />
                    <span className="text-sm font-medium">{option.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Current Color Combination Display */}
      {colorCombination && (
        <div className="absolute top-20 right-4 z-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Current Colors</h4>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: colorCombination.shirt }}
              />
              <span className="text-xs text-gray-700">Shirt</span>
            </div>
            <div className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: colorCombination.pants }}
              />
              <span className="text-xs text-gray-700">Pants</span>
            </div>
            <div className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: colorCombination.shoes }}
              />
              <span className="text-xs text-gray-700">Shoes</span>
            </div>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ height: '100vh' }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <Environment preset="studio" />

        {renderModel()}

        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={10}
        />
      </Canvas>

      {/* No Recommendations Message */}
      {!recommendations && !isLoading && (
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-white/95 backdrop-blur-sm border-t border-gray-200">
          <div className="max-w-7xl mx-auto px-4 py-6 text-center">
            <div className="max-w-md mx-auto">
              <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Color Recommendations Yet</h3>
              <p className="text-sm text-gray-600 mb-4">
                Upload and analyze a face photo to get personalized color recommendations for your 3D model.
              </p>
              <button
                onClick={fetchLatestRecommendations}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Check for Recommendations
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Color Recommendations Panel */}
      {recommendations && recommendations.outfits && (
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-white/95 backdrop-blur-sm border-t border-gray-200">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Latest Color Recommendations ({recommendations.outfits.length} outfits)
              </h3>
              <div className="text-sm text-gray-600">
                AI Service: {recommendations.aiService} |
                Processing: {recommendations.processingTime}ms |
                Confidence: {Math.round(recommendations.confidence * 100)}%
              </div>
            </div>
            
            {/* Outfit Selection */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {recommendations.outfits.map((outfit, index) => (
                <button
                  key={index}
                  onClick={() => handleOutfitChange(index)}
                  className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${
                    selectedOutfitIndex === index
                      ? 'border-blue-500 bg-blue-50 shadow-lg'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="text-left">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{outfit.outfitName}</h4>
                      {selectedOutfitIndex === index && (
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          Applied
                        </span>
                      )}
                    </div>

                    {/* Color Swatches with Labels */}
                    <div className="space-y-2 mb-3">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded-full border border-gray-300 shadow-sm"
                          style={{ backgroundColor: outfit.shirt.hex }}
                        />
                        <span className="text-xs text-gray-700">
                          Shirt: <span className="font-medium">{outfit.shirt.color}</span>
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded-full border border-gray-300 shadow-sm"
                          style={{ backgroundColor: outfit.pants.hex }}
                        />
                        <span className="text-xs text-gray-700">
                          Pants: <span className="font-medium">{outfit.pants.color}</span>
                        </span>
                      </div>
                      {outfit.shoes && (
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-6 h-6 rounded-full border border-gray-300 shadow-sm"
                            style={{ backgroundColor: outfit.shoes.hex }}
                          />
                          <span className="text-xs text-gray-700">
                            Shoes: <span className="font-medium">{outfit.shoes.color}</span>
                          </span>
                        </div>
                      )}
                    </div>

                    <p className="text-xs text-gray-600 leading-relaxed">{outfit.overallReason}</p>
                  </div>
                </button>
              ))}
            </div>

            {/* Additional Color Palette Information */}
            {recommendations.colorPalette && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Best Colors for You</h4>
                    <div className="flex flex-wrap gap-2">
                      {recommendations.colorPalette.bestColors.map((color, index) => (
                        <div
                          key={index}
                          className="w-8 h-8 rounded-full border border-gray-300 shadow-sm"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Colors to Avoid</h4>
                    <div className="flex flex-wrap gap-2">
                      {recommendations.colorPalette.avoidColors.map((color, index) => (
                        <div
                          key={index}
                          className="w-8 h-8 rounded-full border border-gray-300 shadow-sm opacity-60"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                {/* AI Advice */}
                {recommendations.advice && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 mb-1">AI Styling Advice</h4>
                    <p className="text-sm text-blue-800">{recommendations.advice}</p>
                  </div>
                )}

                {/* Seasonal Type */}
                {recommendations.colorPalette.seasonalType && (
                  <div className="mt-2 text-center">
                    <span className="inline-block px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      Your Color Season: {recommendations.colorPalette.seasonalType}
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center z-20">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">Loading recommendations...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
