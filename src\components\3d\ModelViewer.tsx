'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Shirt, User, Spark<PERSON>, <PERSON><PERSON>, <PERSON>ap, <PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON>, Heart, ArrowRight } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationAPI } from '@/lib/api';
import { ColorRecommendation } from '@/types';
import { MaleModel } from './models/MaleModel';
import { FemaleModel } from './models/FemaleModel';
import { FemaleCasualModel } from './models/FemaleCasualModel';
import { FemaleFormalModel } from './models/FemaleFormalModel';
import { FemaleCheongsam } from './models/FemaleCheongsam';
import { toast } from 'react-hot-toast';

interface ModelViewerProps {
  analysisId?: string;
}

type ModelType = 'male' | 'female-casual' | 'female-formal' | 'female-cheongsam';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({ analysisId }) => {
  const { user } = useAuth();
  const [selectedModel, setSelectedModel] = useState<ModelType>('male');
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [selectedOutfitIndex, setSelectedOutfitIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [colorCombination, setColorCombination] = useState<ColorCombination | null>(null);

  // Set default model based on user gender following the exact workflow
  useEffect(() => {
    console.log('🚻 Setting model based on user gender:', user?.gender);

    if (user?.gender === 'female') {
      // For female users, default to casual (can be changed to formal later)
      setSelectedModel('female-casual');
      console.log('👩 Female user detected - setting casual model as default');
    } else {
      // For male users, use the temp.glb model (explore.tsx equivalent)
      setSelectedModel('male');
      console.log('👨 Male user detected - setting male model (temp.glb)');
    }
  }, [user?.gender]);

  // Fetch latest recommendations on component mount
  useEffect(() => {
    fetchLatestRecommendations();
  }, []);

  // Update colors when outfit selection changes
  useEffect(() => {
    if (recommendations?.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const selectedOutfit = recommendations.outfits[selectedOutfitIndex];
      console.log('🎨 Applying colors from outfit:', selectedOutfit);

      setColorCombination({
        shirt: selectedOutfit.shirt.hex,
        pants: selectedOutfit.pants.hex,
        shoes: selectedOutfit.shoes?.hex || selectedOutfit.pants.hex
      });

      toast.success(`Applied ${selectedOutfit.outfitName} colors!`);
    }
  }, [selectedOutfitIndex, recommendations]);

  // Update color combination when recommendations or outfit selection changes
  useEffect(() => {
    if (recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const outfit = recommendations.outfits[selectedOutfitIndex];
      setColorCombination({
        shirt: outfit.shirt.hex,
        pants: outfit.pants.hex,
        shoes: outfit.shoes?.hex || '#8B4513'
      });
    }
  }, [recommendations, selectedOutfitIndex]);

  const fetchLatestRecommendations = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Fetching latest color recommendations from API...');

      // Call the specific API endpoint you provided
      const response = await fetch('https://faceapp-ttwh.onrender.com/api/face/recommendations/latest', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Latest recommendations API response:', data);

        if (data.success && data.data) {
          setRecommendations(data.data);
          setSelectedOutfitIndex(0); // Auto-select first outfit
          console.log('🎨 Color recommendations loaded successfully:', data.data);
          toast.success('Latest color recommendations loaded!');
        } else {
          console.log('⚠️ No recommendations found in response');
          toast.info('No color recommendations found. Please complete a face analysis first.');
        }
      } else {
        console.error('❌ API request failed:', response.status, response.statusText);
        const errorData = await response.json().catch(() => ({}));
        console.error('Error details:', errorData);
        toast.error('Failed to load recommendations. Please try again.');
      }
    } catch (error) {
      console.error('💥 Failed to fetch latest recommendations:', error);
      toast.error('Network error. Please check your connection.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecommendationsForAnalysis = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getRecommendations(id, {
        style: 'casual',
        occasion: 'everyday'
      });
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Analysis recommendations loaded:', response.data);
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch analysis recommendations:', error);
      toast.error('Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelChange = (modelType: ModelType) => {
    setSelectedModel(modelType);
    console.log('ModelViewer: Model changed to:', modelType);
  };

  const handleOutfitChange = (index: number) => {
    setSelectedOutfitIndex(index);
    console.log('ModelViewer: Outfit changed to index:', index);
  };

  const renderModel = () => {
    const modelProps = {
      colorCombination,
      enableColorSync: !!colorCombination
    };

    switch (selectedModel) {
      case 'male':
        return <MaleModel {...modelProps} />;
      case 'female-casual':
        return <FemaleCasualModel {...modelProps} />;
      case 'female-formal':
        return <FemaleFormalModel {...modelProps} />;
      case 'female-cheongsam':
        return <FemaleCheongsam {...modelProps} />;
      default:
        return <MaleModel {...modelProps} />;
    }
  };

  const getModelOptions = () => {
    if (user?.gender === 'female') {
      // Female workflow: Choose category (Formal/Casual/Default)
      return [
        {
          value: 'female-casual',
          label: 'Casual Wear',
          icon: Shirt,
          description: 'casualwear.glb model',
          gradient: 'from-pink-500 to-rose-500'
        },
        {
          value: 'female-formal',
          label: 'Formal Wear',
          icon: User,
          description: 'differentmessformal.glb model',
          gradient: 'from-purple-500 to-indigo-500'
        },
        {
          value: 'female-cheongsam',
          label: 'Default Female',
          icon: Sparkles,
          description: 'female.glb model',
          gradient: 'from-emerald-500 to-teal-500'
        }
      ];
    } else {
      // Male workflow: Direct to explore.tsx → temp.glb model
      return [
        {
          value: 'male',
          label: 'Male Explorer',
          icon: User,
          description: 'temp.glb model',
          gradient: 'from-blue-500 to-cyan-500'
        }
      ];
    }
  };

  return (
    <div className="w-full min-h-screen relative">
      {/* Color Recommendations Panel - Mobile Responsive */}
      {recommendations && recommendations.outfits && (
        <div className="absolute top-4 left-2 right-2 sm:left-4 sm:right-4 z-20 bg-white/10 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-3 sm:p-4 border border-white/20 max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-white">Your Color Recommendations</h3>
            <button
              onClick={fetchLatestRecommendations}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:shadow-lg transition-all duration-300 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
            </button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-3">
            {recommendations.outfits.map((outfit, index) => (
              <button
                key={index}
                onClick={() => {
                  setSelectedOutfitIndex(index);
                  console.log('🎨 Selected outfit:', outfit);
                }}
                className={`group p-2 sm:p-3 rounded-xl sm:rounded-2xl transition-all duration-300 transform hover:scale-105 ${
                  selectedOutfitIndex === index
                    ? 'bg-gradient-to-r from-emerald-500 to-teal-500 shadow-lg'
                    : 'bg-white/10 hover:bg-white/20'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-white font-medium text-xs sm:text-sm truncate">{outfit.outfitName}</h4>
                  {selectedOutfitIndex === index && (
                    <Star className="w-3 h-3 sm:w-4 sm:h-4 text-white animate-pulse" />
                  )}
                </div>

                <div className="flex gap-1 sm:gap-2 mb-2 justify-center">
                  <div
                    className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-white/30 shadow-lg"
                    style={{ backgroundColor: outfit.shirt.hex }}
                    title={`Shirt: ${outfit.shirt.color}`}
                  />
                  <div
                    className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-white/30 shadow-lg"
                    style={{ backgroundColor: outfit.pants.hex }}
                    title={`Pants: ${outfit.pants.color}`}
                  />
                  {outfit.shoes && (
                    <div
                      className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-white/30 shadow-lg"
                      style={{ backgroundColor: outfit.shoes.hex }}
                      title={`Shoes: ${outfit.shoes.color}`}
                    />
                  )}
                </div>

                <p className="text-purple-200 text-xs text-center leading-relaxed hidden sm:block">
                  {outfit.overallReason}
                </p>
              </button>
            ))}
          </div>

          {/* Model Selection */}
          <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-white/20">
            <div className="flex flex-wrap justify-center gap-1 sm:gap-2">
              {getModelOptions().map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    onClick={() => handleModelChange(option.value as ModelType)}
                    className={`group relative overflow-hidden px-2 sm:px-4 py-1 sm:py-2 rounded-lg sm:rounded-xl transition-all duration-300 ${
                      selectedModel === option.value
                        ? `bg-gradient-to-r ${option.gradient} text-white shadow-lg`
                        : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                    }`}
                  >
                    <div className="flex items-center gap-1 sm:gap-2">
                      <IconComponent className="w-3 h-3 sm:w-4 sm:h-4" />
                      <span className="text-xs sm:text-sm font-medium">{option.label}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Current Color Combination Display - Mobile Responsive */}
      {colorCombination && (
        <div className="absolute bottom-4 left-2 right-2 sm:top-32 sm:right-6 sm:left-auto sm:bottom-auto z-10 bg-white/15 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-3 sm:p-6 shadow-2xl border border-white/20 transform hover:scale-105 transition-all duration-300 max-w-xs sm:max-w-none mx-auto sm:mx-0">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Palette className="w-4 h-4 text-white" />
            </div>
            <h4 className="text-sm font-bold text-white">Active Palette</h4>
          </div>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.shirt }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"></div>
              </div>
              <span className="text-sm text-white font-medium">Shirt</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.pants }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse animation-delay-1000"></div>
              </div>
              <span className="text-sm text-white font-medium">Pants</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.shoes }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full animate-pulse animation-delay-2000"></div>
              </div>
              <span className="text-sm text-white font-medium">Shoes</span>
            </div>
          </div>
        </div>
      )}

      {/* 3D Canvas - Responsive */}
      <div className="fixed inset-0 z-0">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
          gl={{ antialias: true, alpha: true }}
          dpr={[1, 2]}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <Environment preset="studio" />

          {renderModel()}

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
            touches={{
              ONE: 2, // ROTATE
              TWO: 1  // DOLLY (zoom)
            }}
          />
        </Canvas>
      </div>

      {/* No Recommendations Message */}
      {!recommendations && !isLoading && (
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/50 to-transparent backdrop-blur-xl">
          <div className="max-w-4xl mx-auto px-6 py-12 text-center">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-20 h-20 mx-auto flex items-center justify-center">
                  <Sparkles className="w-10 h-10 text-white animate-spin" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-3 bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                Complete Your Style Journey
              </h3>
              <p className="text-purple-200 mb-4 max-w-md mx-auto leading-relaxed">
                To see your personalized colors on the 3D model, please complete the workflow:
              </p>
              <div className="text-purple-300 text-sm mb-6 space-y-1">
                <div>1. Register → 2. OTP Verification → 3. Login → 4. Dashboard</div>
                <div>5. Upload Photo → 6. Face Analysis → 7. AI Recommendations</div>
                <div>8. Select Outfit Colors → <span className="text-yellow-300 font-semibold">9. View on 3D Model</span></div>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={fetchLatestRecommendations}
                  className="group relative px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl font-semibold shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center gap-3">
                    <RefreshCw className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500" />
                    <span>Load Latest Recommendations</span>
                    <Star className="w-5 h-5 animate-pulse" />
                  </div>
                </button>

                <a
                  href="/dashboard/new-analysis"
                  className="group relative px-8 py-4 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-2xl font-semibold shadow-2xl hover:shadow-emerald-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center gap-3">
                    <Sparkles className="w-5 h-5 animate-pulse" />
                    <span>Start Face Analysis</span>
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Color Recommendations Panel */}
      {recommendations && recommendations.outfits && (
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/60 via-black/40 to-transparent backdrop-blur-2xl">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">
                      AI Color Recommendations
                    </h3>
                    <p className="text-purple-200 text-sm">
                      {recommendations.outfits.length} personalized outfit combinations
                    </p>
                  </div>
                </div>
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl px-3 py-2 border border-white/20">
                    <span className="text-purple-200">AI: </span>
                    <span className="text-white font-medium">{recommendations.aiService}</span>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl px-3 py-2 border border-white/20">
                    <span className="text-purple-200">Speed: </span>
                    <span className="text-white font-medium">{recommendations.processingTime}ms</span>
                  </div>
                  <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-xl px-3 py-2 border border-green-400/30">
                    <span className="text-green-200">Confidence: </span>
                    <span className="text-green-100 font-bold">{Math.round(recommendations.confidence * 100)}%</span>
                  </div>
                </div>
              </div>

              {/* Simple outfit grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recommendations.outfits.map((outfit, index) => (
                  <button
                    key={index}
                    onClick={() => handleOutfitChange(index)}
                    className={`p-4 rounded-2xl transition-all duration-300 ${
                      selectedOutfitIndex === index
                        ? 'bg-gradient-to-br from-purple-500/30 to-pink-500/30 border-2 border-purple-400/50'
                        : 'bg-white/10 border-2 border-white/20 hover:bg-white/20'
                    }`}
                  >
                    <div className="text-left">
                      <h4 className="font-bold text-white mb-2">{outfit.outfitName}</h4>
                      <div className="flex gap-2 mb-2">
                        <div
                          className="w-6 h-6 rounded-full border border-white/30"
                          style={{ backgroundColor: outfit.shirt.hex }}
                        />
                        <div
                          className="w-6 h-6 rounded-full border border-white/30"
                          style={{ backgroundColor: outfit.pants.hex }}
                        />
                        {outfit.shoes && (
                          <div
                            className="w-6 h-6 rounded-full border border-white/30"
                            style={{ backgroundColor: outfit.shoes.hex }}
                          />
                        )}
                      </div>
                      <p className="text-xs text-purple-200">{outfit.overallReason}</p>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
            


      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/60 backdrop-blur-xl flex items-center justify-center z-20">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20 text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
                <div className="absolute inset-2 border-4 border-transparent border-t-pink-500 rounded-full animate-spin animation-delay-150"></div>
                <div className="absolute inset-4 border-4 border-transparent border-t-blue-500 rounded-full animate-spin animation-delay-300"></div>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-bold text-white">Analyzing Colors</h3>
              <p className="text-purple-200">AI is crafting your perfect palette...</p>
              <div className="flex justify-center space-x-1 mt-4">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce animation-delay-100"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
