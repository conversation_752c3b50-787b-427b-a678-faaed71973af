'use client';

import React from 'react';
import Link from 'next/link';
import { 
  UserPlus, 
  Mail, 
  LogIn, 
  Home, 
  Upload, 
  Brain, 
  <PERSON>rk<PERSON>, 
  <PERSON>lette, 
  Eye, 
  RotateCcw,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

interface WorkflowStep {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  link?: string;
  status: 'completed' | 'current' | 'upcoming';
}

interface WorkflowGuideProps {
  currentStep?: number;
  completedSteps?: number[];
  showProgress?: boolean;
}

export const WorkflowGuide: React.FC<WorkflowGuideProps> = ({
  currentStep = 1,
  completedSteps = [],
  showProgress = true
}) => {
  const steps: WorkflowStep[] = [
    {
      id: 1,
      title: 'Register',
      description: 'Create your account',
      icon: UserPlus,
      link: '/auth',
      status: completedSteps.includes(1) ? 'completed' : currentStep === 1 ? 'current' : 'upcoming'
    },
    {
      id: 2,
      title: 'OTP Verification',
      description: 'Verify your email',
      icon: Mail,
      status: completedSteps.includes(2) ? 'completed' : currentStep === 2 ? 'current' : 'upcoming'
    },
    {
      id: 3,
      title: 'Login',
      description: 'Access your account',
      icon: LogIn,
      link: '/auth',
      status: completedSteps.includes(3) ? 'completed' : currentStep === 3 ? 'current' : 'upcoming'
    },
    {
      id: 4,
      title: 'Dashboard',
      description: 'Your style hub',
      icon: Home,
      link: '/dashboard',
      status: completedSteps.includes(4) ? 'completed' : currentStep === 4 ? 'current' : 'upcoming'
    },
    {
      id: 5,
      title: 'Upload Photo',
      description: 'Share your best selfie',
      icon: Upload,
      link: '/dashboard/new-analysis',
      status: completedSteps.includes(5) ? 'completed' : currentStep === 5 ? 'current' : 'upcoming'
    },
    {
      id: 6,
      title: 'Face Analysis',
      description: 'AI analyzes your features',
      icon: Brain,
      status: completedSteps.includes(6) ? 'completed' : currentStep === 6 ? 'current' : 'upcoming'
    },
    {
      id: 7,
      title: 'AI Recommendations',
      description: 'Get personalized colors',
      icon: Sparkles,
      status: completedSteps.includes(7) ? 'completed' : currentStep === 7 ? 'current' : 'upcoming'
    },
    {
      id: 8,
      title: 'Select Outfit Colors',
      description: 'Choose your favorite palette',
      icon: Palette,
      status: completedSteps.includes(8) ? 'completed' : currentStep === 8 ? 'current' : 'upcoming'
    },
    {
      id: 9,
      title: 'View on 3D Model',
      description: 'See colors come to life',
      icon: Eye,
      link: '/model-viewer',
      status: completedSteps.includes(9) ? 'completed' : currentStep === 9 ? 'current' : 'upcoming'
    },
    {
      id: 10,
      title: 'Interactive Experience',
      description: 'Touch, zoom, rotate',
      icon: RotateCcw,
      status: completedSteps.includes(10) ? 'completed' : currentStep === 10 ? 'current' : 'upcoming'
    }
  ];

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'from-emerald-500 to-teal-500';
      case 'current':
        return 'from-purple-500 to-pink-500';
      default:
        return 'from-gray-400 to-gray-500';
    }
  };

  const getStepTextColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-emerald-300';
      case 'current':
        return 'text-white';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
          <Sparkles className="w-5 h-5 text-white" />
        </div>
        <h3 className="text-xl font-bold text-white">Your Style Journey</h3>
      </div>

      <div className="space-y-4">
        {steps.map((step, index) => {
          const IconComponent = step.icon;
          const isLast = index === steps.length - 1;
          
          return (
            <div key={step.id} className="relative">
              <div className="flex items-center gap-4">
                {/* Step Icon */}
                <div className={`relative z-10 p-3 bg-gradient-to-r ${getStepColor(step.status)} rounded-xl transition-all duration-300`}>
                  {step.status === 'completed' ? (
                    <CheckCircle className="w-5 h-5 text-white" />
                  ) : (
                    <IconComponent className="w-5 h-5 text-white" />
                  )}
                </div>

                {/* Step Content */}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className={`font-semibold ${getStepTextColor(step.status)}`}>
                        {step.title}
                      </h4>
                      <p className="text-purple-200 text-sm">{step.description}</p>
                    </div>
                    
                    {/* Action Button */}
                    {step.link && (step.status === 'current' || step.status === 'upcoming') && (
                      <Link
                        href={step.link}
                        className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 text-sm"
                      >
                        Go
                        <ArrowRight className="w-4 h-4" />
                      </Link>
                    )}
                  </div>
                </div>
              </div>

              {/* Connecting Line */}
              {!isLast && (
                <div className="absolute left-6 top-12 w-0.5 h-8 bg-gradient-to-b from-purple-500/50 to-transparent"></div>
              )}
            </div>
          );
        })}
      </div>

      {/* Progress Bar */}
      {showProgress && (
        <div className="mt-6 pt-6 border-t border-white/20">
          <div className="flex items-center justify-between mb-2">
            <span className="text-purple-200 text-sm">Progress</span>
            <span className="text-white text-sm font-medium">
              {completedSteps.length}/{steps.length} Complete
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${(completedSteps.length / steps.length) * 100}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};
