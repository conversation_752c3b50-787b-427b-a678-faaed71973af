'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface WorkflowContextType {
  currentStep: number;
  completedSteps: number[];
  markStepComplete: (step: number) => void;
  setCurrentStep: (step: number) => void;
  isStepCompleted: (step: number) => boolean;
  getWorkflowProgress: () => number;
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(undefined);

export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (context === undefined) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
};

interface WorkflowProviderProps {
  children: React.ReactNode;
}

export const WorkflowProvider: React.FC<WorkflowProviderProps> = ({ children }) => {
  const [currentStep, setCurrentStepState] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  // Load workflow state from localStorage on mount
  useEffect(() => {
    const savedCurrentStep = localStorage.getItem('workflow_current_step');
    const savedCompletedSteps = localStorage.getItem('workflow_completed_steps');
    
    if (savedCurrentStep) {
      setCurrentStepState(parseInt(savedCurrentStep));
    }
    
    if (savedCompletedSteps) {
      try {
        setCompletedSteps(JSON.parse(savedCompletedSteps));
      } catch (error) {
        console.error('Failed to parse completed steps:', error);
      }
    }
  }, []);

  // Save workflow state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('workflow_current_step', currentStep.toString());
  }, [currentStep]);

  useEffect(() => {
    localStorage.setItem('workflow_completed_steps', JSON.stringify(completedSteps));
  }, [completedSteps]);

  const markStepComplete = (step: number) => {
    setCompletedSteps(prev => {
      if (!prev.includes(step)) {
        const newCompleted = [...prev, step].sort((a, b) => a - b);
        console.log(`✅ Workflow: Step ${step} marked as complete. Completed steps:`, newCompleted);
        return newCompleted;
      }
      return prev;
    });
  };

  const setCurrentStep = (step: number) => {
    setCurrentStepState(step);
    console.log(`🔄 Workflow: Current step set to ${step}`);
    
    // Auto-mark previous steps as completed if they aren't already
    for (let i = 1; i < step; i++) {
      markStepComplete(i);
    }
  };

  const isStepCompleted = (step: number) => {
    return completedSteps.includes(step);
  };

  const getWorkflowProgress = () => {
    const totalSteps = 10; // Total workflow steps
    return (completedSteps.length / totalSteps) * 100;
  };

  const value: WorkflowContextType = {
    currentStep,
    completedSteps,
    markStepComplete,
    setCurrentStep,
    isStepCompleted,
    getWorkflowProgress
  };

  return (
    <WorkflowContext.Provider value={value}>
      {children}
    </WorkflowContext.Provider>
  );
};
