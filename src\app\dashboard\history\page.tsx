'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { faceAPI, recommendationAPI } from '@/lib/api';
import { FaceAnalysis, ColorRecommendation } from '@/types';
import { PageLayout } from '@/components/layout/PageLayout';
import { getAnalysisSummary } from '@/utils/analysisHelpers';
import { 
  History, 
  Camera, 
  Palette, 
  Calendar, 
  Eye, 
  Download, 
  Share2, 
  Filter,
  Search,
  Clock,
  Star,
  ArrowRight,
  Trash2,
  MoreVertical
} from 'lucide-react';
import { toast } from 'react-hot-toast';

export default function HistoryPage() {
  const [analyses, setAnalyses] = useState<FaceAnalysis[]>([]);
  const [recommendations, setRecommendations] = useState<ColorRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'analyses' | 'recommendations'>('all');

  useEffect(() => {
    loadHistory();
  }, [currentPage, filterType]);

  const loadHistory = async () => {
    try {
      setIsLoading(true);
      
      // Load analyses
      if (filterType === 'all' || filterType === 'analyses') {
        const analysesResponse = await faceAPI.getHistory(currentPage, 10);
        if (analysesResponse.success && analysesResponse.data) {
          setAnalyses(analysesResponse.data.analyses);
          setTotalPages(Math.ceil(analysesResponse.data.total / 10));
        }
      }

      // Load recommendations
      if (filterType === 'all' || filterType === 'recommendations') {
        try {
          const recommendationsResponse = await recommendationAPI.getLatestRecommendation();
          if (recommendationsResponse.success && recommendationsResponse.data) {
            setRecommendations([recommendationsResponse.data]);
          }
        } catch (error) {
          console.log('No recommendations found');
        }
      }

    } catch (error: any) {
      console.error('Failed to load history:', error);
      toast.error('Failed to load history');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDeleteAnalysis = async (analysisId: string) => {
    if (!confirm('Are you sure you want to delete this analysis?')) return;
    
    try {
      // Note: You'll need to implement delete endpoint in your API
      toast.success('Analysis deleted successfully');
      loadHistory(); // Reload the list
    } catch (error) {
      toast.error('Failed to delete analysis');
    }
  };

  const filteredAnalyses = analyses.filter(analysis =>
    analysis._id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    formatDate(analysis.createdAt).toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Loading History</h3>
            <p className="text-purple-200">Fetching your analysis history...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title="Analysis History"
      description="Review your past face analyses and color recommendations"
    >
      <div className="max-w-6xl mx-auto px-6 pb-12">
        {/* Filters and Search */}
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-purple-300" />
              <input
                type="text"
                placeholder="Search analyses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400"
              />
            </div>

            {/* Filter Buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => setFilterType('all')}
                className={`px-4 py-2 rounded-xl transition-all duration-300 ${
                  filterType === 'all'
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                    : 'bg-white/10 text-purple-200 hover:bg-white/20'
                }`}
              >
                All
              </button>
              <button
                onClick={() => setFilterType('analyses')}
                className={`px-4 py-2 rounded-xl transition-all duration-300 ${
                  filterType === 'analyses'
                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
                    : 'bg-white/10 text-purple-200 hover:bg-white/20'
                }`}
              >
                Analyses
              </button>
              <button
                onClick={() => setFilterType('recommendations')}
                className={`px-4 py-2 rounded-xl transition-all duration-300 ${
                  filterType === 'recommendations'
                    ? 'bg-gradient-to-r from-emerald-500 to-teal-500 text-white'
                    : 'bg-white/10 text-purple-200 hover:bg-white/20'
                }`}
              >
                Colors
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl">
                <Camera className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">{analyses.length}</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Total Analyses</h3>
            <p className="text-purple-200 text-sm">Face scans completed</p>
          </div>

          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl">
                <Palette className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">{recommendations.length}</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Color Sets</h3>
            <p className="text-purple-200 text-sm">Recommendation collections</p>
          </div>

          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                <Star className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">
                {recommendations[0] ? Math.round(recommendations[0].confidence * 100) : 0}%
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Best Match</h3>
            <p className="text-purple-200 text-sm">Highest confidence score</p>
          </div>
        </div>

        {/* History Items */}
        <div className="space-y-6">
          {/* Color Recommendations */}
          {(filterType === 'all' || filterType === 'recommendations') && recommendations.map((recommendation, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl">
                    <Palette className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">Color Recommendations</h3>
                    <p className="text-purple-200 text-sm">
                      {recommendation.outfits?.length || 0} outfit combinations • 
                      {Math.round(recommendation.confidence * 100)}% confidence
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Link
                    href="/model-viewer"
                    className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:shadow-lg transition-all duration-300"
                  >
                    <Eye className="w-4 h-4" />
                    View in 3D
                  </Link>
                  <button className="p-2 text-purple-300 hover:text-white hover:bg-white/10 rounded-xl transition-colors">
                    <MoreVertical className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Color Outfits Preview */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recommendation.outfits?.slice(0, 3).map((outfit, outfitIndex) => (
                  <div key={outfitIndex} className="p-4 bg-white/5 rounded-2xl">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium">{outfit.outfitName}</h4>
                      <div className="flex gap-2">
                        <div
                          className="w-6 h-6 rounded-full border border-white/30"
                          style={{ backgroundColor: outfit.shirt.hex }}
                        />
                        <div
                          className="w-6 h-6 rounded-full border border-white/30"
                          style={{ backgroundColor: outfit.pants.hex }}
                        />
                        {outfit.shoes && (
                          <div
                            className="w-6 h-6 rounded-full border border-white/30"
                            style={{ backgroundColor: outfit.shoes.hex }}
                          />
                        )}
                      </div>
                    </div>
                    <p className="text-purple-200 text-sm">{outfit.overallReason}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* Face Analyses */}
          {(filterType === 'all' || filterType === 'analyses') && filteredAnalyses.map((analysis, index) => (
            <div key={analysis._id} className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl">
                    <Camera className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">Face Analysis #{index + 1}</h3>
                    <div className="flex items-center gap-4 text-purple-200 text-sm">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {formatDate(analysis.createdAt)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        Processing: {analysis.processingTime || 'N/A'}ms
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => {
                      // Generate recommendations for this analysis
                      toast.success('Generating new recommendations...');
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-xl hover:shadow-lg transition-all duration-300"
                  >
                    <Palette className="w-4 h-4" />
                    Get Colors
                  </button>
                  <button
                    onClick={() => handleDeleteAnalysis(analysis._id)}
                    className="p-2 text-red-300 hover:text-red-200 hover:bg-red-500/10 rounded-xl transition-colors"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Analysis Details */}
              {(() => {
                const summary = getAnalysisSummary(analysis);
                return (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-white/5 rounded-2xl">
                      <h4 className="text-white font-medium mb-2">Skin Tone</h4>
                      <p className="text-purple-200 text-sm capitalize">
                        {summary.skinTone.name}
                      </p>
                      {summary.skinTone.hex && (
                        <div className="flex items-center gap-2 mt-2">
                          <div
                            className="w-4 h-4 rounded-full border border-white/30"
                            style={{ backgroundColor: summary.skinTone.hex }}
                          />
                          <span className="text-xs text-purple-300">{summary.skinTone.hex}</span>
                        </div>
                      )}
                      <p className="text-xs text-purple-400 mt-1">{summary.skinTone.confidence}</p>
                    </div>
                    <div className="p-4 bg-white/5 rounded-2xl">
                      <h4 className="text-white font-medium mb-2">Eye Color</h4>
                      <p className="text-purple-200 text-sm capitalize">
                        {summary.eyeColor.name}
                      </p>
                      {summary.eyeColor.hex && (
                        <div className="flex items-center gap-2 mt-2">
                          <div
                            className="w-4 h-4 rounded-full border border-white/30"
                            style={{ backgroundColor: summary.eyeColor.hex }}
                          />
                          <span className="text-xs text-purple-300">{summary.eyeColor.hex}</span>
                        </div>
                      )}
                      <p className="text-xs text-purple-400 mt-1">{summary.eyeColor.confidence}</p>
                    </div>
                    <div className="p-4 bg-white/5 rounded-2xl">
                      <h4 className="text-white font-medium mb-2">Hair Color</h4>
                      <p className="text-purple-200 text-sm capitalize">
                        {summary.hairColor.name}
                      </p>
                      {summary.hairColor.hex && (
                        <div className="flex items-center gap-2 mt-2">
                          <div
                            className="w-4 h-4 rounded-full border border-white/30"
                            style={{ backgroundColor: summary.hairColor.hex }}
                          />
                          <span className="text-xs text-purple-300">{summary.hairColor.hex}</span>
                        </div>
                      )}
                      <p className="text-xs text-purple-400 mt-1">{summary.hairColor.confidence}</p>
                    </div>
                  </div>
                );
              })()}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredAnalyses.length === 0 && recommendations.length === 0 && (
          <div className="text-center py-12">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30"></div>
              <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-20 h-20 mx-auto flex items-center justify-center">
                <History className="w-10 h-10 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">No History Yet</h3>
            <p className="text-purple-200 mb-6 max-w-md mx-auto">
              Start your style journey by uploading your first photo for analysis.
            </p>
            <Link
              href="/dashboard/new-analysis"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl font-semibold shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 transition-all duration-300"
            >
              <Camera className="w-5 h-5" />
              Start First Analysis
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-4 py-2 rounded-xl transition-all duration-300 ${
                    currentPage === page
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white/10 text-purple-200 hover:bg-white/20'
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </PageLayout>
  );
}
