'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Shi<PERSON>, User, Spark<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON>, Heart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationAPI } from '@/lib/api';
import { ColorRecommendation } from '@/types';
import { MaleModel } from './models/MaleModel';
import { FemaleModel } from './models/FemaleModel';
import { FemaleCasualModel } from './models/FemaleCasualModel';
import { FemaleFormalModel } from './models/FemaleFormalModel';
import { FemaleCheongsam } from './models/FemaleCheongsam';
import { toast } from 'react-hot-toast';

interface ModelViewerProps {
  analysisId?: string;
}

type ModelType = 'male' | 'female-casual' | 'female-formal' | 'female-cheongsam';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({ analysisId }) => {
  const { user } = useAuth();
  const [selectedModel, setSelectedModel] = useState<ModelType>('male');
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [selectedOutfitIndex, setSelectedOutfitIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [colorCombination, setColorCombination] = useState<ColorCombination | null>(null);

  // Set default model based on user gender
  useEffect(() => {
    if (user?.gender === 'female') {
      setSelectedModel('female-casual');
    } else {
      setSelectedModel('male');
    }
  }, [user?.gender]);

  // Fetch latest recommendations on component mount
  useEffect(() => {
    fetchLatestRecommendations();
  }, []);

  // Update color combination when recommendations or outfit selection changes
  useEffect(() => {
    if (recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const outfit = recommendations.outfits[selectedOutfitIndex];
      setColorCombination({
        shirt: outfit.shirt.hex,
        pants: outfit.pants.hex,
        shoes: outfit.shoes?.hex || '#8B4513'
      });
    }
  }, [recommendations, selectedOutfitIndex]);

  const fetchLatestRecommendations = async () => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getLatestRecommendation();
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Latest recommendations loaded:', response.data);
      } else {
        console.log('ModelViewer: No recommendations found');
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecommendationsForAnalysis = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await recommendationAPI.getRecommendations(id, {
        style: 'casual',
        occasion: 'everyday'
      });
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        console.log('ModelViewer: Analysis recommendations loaded:', response.data);
      }
    } catch (error) {
      console.error('ModelViewer: Failed to fetch analysis recommendations:', error);
      toast.error('Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelChange = (modelType: ModelType) => {
    setSelectedModel(modelType);
    console.log('ModelViewer: Model changed to:', modelType);
  };

  const handleOutfitChange = (index: number) => {
    setSelectedOutfitIndex(index);
    console.log('ModelViewer: Outfit changed to index:', index);
  };

  const renderModel = () => {
    const modelProps = {
      colorCombination,
      enableColorSync: !!colorCombination
    };

    switch (selectedModel) {
      case 'male':
        return <MaleModel {...modelProps} />;
      case 'female-casual':
        return <FemaleCasualModel {...modelProps} />;
      case 'female-formal':
        return <FemaleFormalModel {...modelProps} />;
      case 'female-cheongsam':
        return <FemaleCheongsam {...modelProps} />;
      default:
        return <MaleModel {...modelProps} />;
    }
  };

  const getModelOptions = () => {
    if (user?.gender === 'female') {
      return [
        { 
          value: 'female-casual', 
          label: 'Casual Wear', 
          icon: Shirt,
          description: 'Everyday comfort style',
          gradient: 'from-pink-500 to-rose-500'
        },
        { 
          value: 'female-formal', 
          label: 'Formal Wear', 
          icon: User,
          description: 'Professional elegance',
          gradient: 'from-purple-500 to-indigo-500'
        },
        { 
          value: 'female-cheongsam', 
          label: 'One Piece', 
          icon: Sparkles,
          description: 'Traditional beauty',
          gradient: 'from-emerald-500 to-teal-500'
        }
      ];
    } else {
      return [
        { 
          value: 'male', 
          label: 'Male Model', 
          icon: User,
          description: 'Classic masculine style',
          gradient: 'from-blue-500 to-cyan-500'
        }
      ];
    }
  };

  return (
    <div className="w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start gap-3 mb-2">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                  <Eye className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                  3D Style Studio
                </h1>
              </div>
              <p className="text-purple-200 text-sm lg:text-base">
                {user?.gender === 'female' ? '✨ Explore Your Style Universe' : '🚀 Discover Your Look'} • 
                Interactive color magic awaits
              </p>
            </div>
            
            {/* Model Selection */}
            <div className="flex flex-wrap justify-center lg:justify-end gap-3">
              {getModelOptions().map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    onClick={() => handleModelChange(option.value as ModelType)}
                    className={`group relative overflow-hidden px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 ${
                      selectedModel === option.value
                        ? `bg-gradient-to-r ${option.gradient} text-white shadow-2xl shadow-purple-500/25`
                        : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <IconComponent className="w-5 h-5" />
                      <div className="text-left">
                        <div className="text-sm font-semibold">{option.label}</div>
                        <div className="text-xs opacity-80">{option.description}</div>
                      </div>
                    </div>
                    {selectedModel === option.value && (
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-50"></div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Current Color Combination Display */}
      {colorCombination && (
        <div className="absolute top-32 right-6 z-10 bg-white/15 backdrop-blur-xl rounded-3xl p-6 shadow-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Palette className="w-4 h-4 text-white" />
            </div>
            <h4 className="text-sm font-bold text-white">Active Palette</h4>
          </div>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.shirt }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"></div>
              </div>
              <span className="text-sm text-white font-medium">Shirt</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.pants }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse animation-delay-1000"></div>
              </div>
              <span className="text-sm text-white font-medium">Pants</span>
            </div>
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div
                  className="w-8 h-8 rounded-2xl border-2 border-white/30 shadow-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ backgroundColor: colorCombination.shoes }}
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full animate-pulse animation-delay-2000"></div>
              </div>
              <span className="text-sm text-white font-medium">Shoes</span>
            </div>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ height: '100vh' }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <Environment preset="studio" />
        
        {renderModel()}
        
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={10}
        />
      </Canvas>

      {/* Rest of the component will be added in next edit */}
    </div>
  );
};
