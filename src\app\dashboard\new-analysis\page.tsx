'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { faceAPI, recommendationAPI } from '@/lib/api';
import { FaceAnalysis, ColorRecommendation } from '@/types';
import { PageLayout } from '@/components/layout/PageLayout';
import { ImageUpload } from '@/components/face/ImageUpload';
import { WorkflowGuide } from '@/components/workflow/WorkflowGuide';
import { 
  Upload, 
  Camera, 
  Sparkles, 
  Zap, 
  CheckCircle, 
  ArrowRight,
  Eye,
  Palette,
  Star,
  Clock,
  Brain
} from 'lucide-react';
import { toast } from 'react-hot-toast';

type AnalysisStep = 'upload' | 'analyzing' | 'results' | 'recommendations';

export default function NewAnalysisPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<AnalysisStep>('upload');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGeneratingRecommendations, setIsGeneratingRecommendations] = useState(false);
  const [analysis, setAnalysis] = useState<FaceAnalysis | null>(null);
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  const handleImageUpload = async (imageUrl: string, publicId: string) => {
    try {
      setIsAnalyzing(true);
      setCurrentStep('analyzing');
      setAnalysisProgress(0);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);

      console.log('Starting face analysis for:', imageUrl);

      // Try the new URL-based analysis first
      let response;
      try {
        response = await faceAPI.analyzeFaceFromUrl(imageUrl, 'uploaded-image.jpg');
        console.log('URL-based analysis response:', response);
      } catch (urlError) {
        console.log('URL-based analysis failed, trying direct method...', urlError);
        // Fallback to direct analysis
        response = await faceAPI.analyzeFace({ imageUrl, publicId });
        console.log('Direct analysis response:', response);
      }

      clearInterval(progressInterval);
      setAnalysisProgress(100);

      if (response.success && response.data) {
        // Handle both response formats
        const analysisData = (response.data as any).analysis || response.data;
        console.log('Analysis successful:', analysisData);
        setAnalysis(analysisData as FaceAnalysis);
        setCurrentStep('results');
        toast.success('Face analysis completed!');
      } else {
        console.error('Analysis failed:', response);
        toast.error(response.message || 'Analysis failed');
        setCurrentStep('upload');
      }
    } catch (error: any) {
      console.error('Analysis error:', error);
      toast.error(error.response?.data?.message || error.message || 'Analysis failed');
      setCurrentStep('upload');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleGenerateRecommendations = async () => {
    if (!analysis) return;

    try {
      setIsGeneratingRecommendations(true);
      const response = await recommendationAPI.getRecommendations(
        analysis._id,
        { style: 'professional', occasion: 'business' }
      );
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        setCurrentStep('recommendations');
        toast.success('Color recommendations generated!');
      } else {
        toast.error(response.message || 'Failed to generate recommendations');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to generate recommendations');
    } finally {
      setIsGeneratingRecommendations(false);
    }
  };

  const renderUploadStep = () => (
    <div className="max-w-4xl mx-auto px-6">
      {/* Workflow Guide */}
      <div className="mb-8">
        <WorkflowGuide
          currentStep={5}
          completedSteps={[1, 2, 3, 4]}
        />
      </div>

      <div className="text-center mb-12">
        <div className="relative mb-6">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
          <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-20 h-20 mx-auto flex items-center justify-center">
            <Upload className="w-10 h-10 text-white" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">Upload Your Photo</h2>
        <p className="text-purple-200 text-lg max-w-2xl mx-auto">
          Upload a clear, well-lit photo of your face to discover your perfect color palette. 
          Our AI will analyze your skin tone, eye color, and hair color.
        </p>
      </div>

      <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20">
        <ImageUpload
          onUploadSuccess={handleImageUpload}
          isAnalyzing={isAnalyzing}
        />
      </div>

      {/* Tips */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center p-6 bg-white/5 rounded-2xl">
          <Camera className="w-8 h-8 text-purple-300 mx-auto mb-3" />
          <h3 className="text-white font-semibold mb-2">Good Lighting</h3>
          <p className="text-purple-200 text-sm">Use natural light or bright indoor lighting</p>
        </div>
        <div className="text-center p-6 bg-white/5 rounded-2xl">
          <Eye className="w-8 h-8 text-purple-300 mx-auto mb-3" />
          <h3 className="text-white font-semibold mb-2">Clear Face</h3>
          <p className="text-purple-200 text-sm">Make sure your face is clearly visible</p>
        </div>
        <div className="text-center p-6 bg-white/5 rounded-2xl">
          <Sparkles className="w-8 h-8 text-purple-300 mx-auto mb-3" />
          <h3 className="text-white font-semibold mb-2">No Makeup</h3>
          <p className="text-purple-200 text-sm">For best results, use a photo without makeup</p>
        </div>
      </div>
    </div>
  );

  const renderAnalyzingStep = () => (
    <div className="max-w-2xl mx-auto px-6 text-center">
      {/* Workflow Guide */}
      <div className="mb-8">
        <WorkflowGuide
          currentStep={6}
          completedSteps={[1, 2, 3, 4, 5]}
        />
      </div>
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
        <div className="relative w-24 h-24 mx-auto">
          <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
          <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
          <div className="absolute inset-2 border-4 border-transparent border-t-pink-500 rounded-full animate-spin animation-delay-150"></div>
          <div className="absolute inset-4 border-4 border-transparent border-t-blue-500 rounded-full animate-spin animation-delay-300"></div>
        </div>
      </div>

      <h2 className="text-3xl font-bold text-white mb-4">Analyzing Your Photo</h2>
      <p className="text-purple-200 text-lg mb-8">
        Our AI is analyzing your facial features to determine your perfect color palette...
      </p>

      {/* Progress Bar */}
      <div className="bg-white/10 rounded-full h-3 mb-6 overflow-hidden">
        <div 
          className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-500 ease-out"
          style={{ width: `${analysisProgress}%` }}
        />
      </div>
      <p className="text-purple-300 text-sm">{analysisProgress}% Complete</p>

      {/* Analysis Steps */}
      <div className="mt-12 space-y-4">
        <div className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl">
          <Brain className="w-6 h-6 text-purple-300" />
          <span className="text-white">Detecting facial features...</span>
          {analysisProgress > 30 && <CheckCircle className="w-5 h-5 text-emerald-400 ml-auto" />}
        </div>
        <div className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl">
          <Palette className="w-6 h-6 text-purple-300" />
          <span className="text-white">Analyzing skin tone...</span>
          {analysisProgress > 60 && <CheckCircle className="w-5 h-5 text-emerald-400 ml-auto" />}
        </div>
        <div className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl">
          <Star className="w-6 h-6 text-purple-300" />
          <span className="text-white">Generating color profile...</span>
          {analysisProgress > 90 && <CheckCircle className="w-5 h-5 text-emerald-400 ml-auto" />}
        </div>
      </div>
    </div>
  );

  const renderResultsStep = () => (
    <div className="max-w-4xl mx-auto px-6">
      {/* Workflow Guide */}
      <div className="mb-8">
        <WorkflowGuide
          currentStep={7}
          completedSteps={[1, 2, 3, 4, 5, 6]}
        />
      </div>
      <div className="text-center mb-8">
        <div className="relative mb-6">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
          <div className="relative p-4 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl w-20 h-20 mx-auto flex items-center justify-center">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">Analysis Complete!</h2>
        <p className="text-purple-200 text-lg">
          We've successfully analyzed your facial features. Here are the results:
        </p>
      </div>

      {analysis && (
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-white/5 rounded-2xl">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Palette className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-white font-bold mb-2">Skin Tone</h3>
              <p className="text-purple-200 capitalize">
                {analysis.skinTone?.undertone || 'Not detected'}
              </p>
            </div>
            <div className="text-center p-6 bg-white/5 rounded-2xl">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Eye className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-white font-bold mb-2">Eye Color</h3>
              <p className="text-purple-200 capitalize">
                {analysis.eyeColor?.dominant || 'Not detected'}
              </p>
            </div>
            <div className="text-center p-6 bg-white/5 rounded-2xl">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-white font-bold mb-2">Hair Color</h3>
              <p className="text-purple-200 capitalize">
                {analysis.hairColor?.dominant || 'Not detected'}
              </p>
            </div>
          </div>

          <div className="text-center mt-8">
            <button
              onClick={handleGenerateRecommendations}
              disabled={isGeneratingRecommendations}
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl font-semibold shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 transition-all duration-300 disabled:opacity-50"
            >
              <Zap className="w-5 h-5" />
              {isGeneratingRecommendations ? 'Generating Colors...' : 'Generate Color Recommendations'}
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}
    </div>
  );

  const renderRecommendationsStep = () => (
    <div className="max-w-6xl mx-auto px-6">
      {/* Workflow Guide */}
      <div className="mb-8">
        <WorkflowGuide
          currentStep={8}
          completedSteps={[1, 2, 3, 4, 5, 6, 7]}
        />
      </div>
      <div className="text-center mb-8">
        <div className="relative mb-6">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
          <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-20 h-20 mx-auto flex items-center justify-center">
            <Palette className="w-10 h-10 text-white" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">Your Perfect Colors!</h2>
        <p className="text-purple-200 text-lg">
          Based on your analysis, here are personalized color recommendations just for you.
        </p>
      </div>

      {recommendations && (
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recommendations.outfits?.map((outfit, index) => (
              <div key={index} className="p-6 bg-white/5 rounded-2xl">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-white font-bold">{outfit.outfitName}</h3>
                  <div className="flex gap-2">
                    <div
                      className="w-8 h-8 rounded-full border border-white/30"
                      style={{ backgroundColor: outfit.shirt.hex }}
                    />
                    <div
                      className="w-8 h-8 rounded-full border border-white/30"
                      style={{ backgroundColor: outfit.pants.hex }}
                    />
                    {outfit.shoes && (
                      <div
                        className="w-8 h-8 rounded-full border border-white/30"
                        style={{ backgroundColor: outfit.shoes.hex }}
                      />
                    )}
                  </div>
                </div>
                <p className="text-purple-200 text-sm mb-4">{outfit.overallReason}</p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: outfit.shirt.hex }}
                    />
                    <span className="text-white text-sm">{outfit.shirt.color}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: outfit.pants.hex }}
                    />
                    <span className="text-white text-sm">{outfit.pants.color}</span>
                  </div>
                  {outfit.shoes && (
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: outfit.shoes.hex }}
                      />
                      <span className="text-white text-sm">{outfit.shoes.color}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="text-center space-y-4">
        <button
          onClick={() => router.push('/model-viewer')}
          className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-2xl font-semibold shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 transition-all duration-300 mr-4"
        >
          <Eye className="w-5 h-5" />
          Try Colors in 3D
          <ArrowRight className="w-5 h-5" />
        </button>
        <button
          onClick={() => {
            setCurrentStep('upload');
            setAnalysis(null);
            setRecommendations(null);
          }}
          className="inline-flex items-center gap-3 px-8 py-4 bg-white/10 text-white rounded-2xl font-semibold hover:bg-white/20 transition-all duration-300"
        >
          <Upload className="w-5 h-5" />
          Analyze Another Photo
        </button>
      </div>
    </div>
  );

  return (
    <PageLayout>
      <div className="pb-12">
        {currentStep === 'upload' && renderUploadStep()}
        {currentStep === 'analyzing' && renderAnalyzingStep()}
        {currentStep === 'results' && renderResultsStep()}
        {currentStep === 'recommendations' && renderRecommendationsStep()}
      </div>
    </PageLayout>
  );
}
