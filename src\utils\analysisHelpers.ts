import { FaceAnalysis } from '@/types';

/**
 * Helper functions to extract color data from face analysis response
 * Handles both new API format and legacy format for backward compatibility
 */

export interface ColorData {
  primary: string;
  hex: string;
  rgb: { r: number; g: number; b: number };
  confidence?: number;
}

export const getSkinTone = (analysis: FaceAnalysis): ColorData | null => {
  // New API format
  if (analysis.colors?.skinTone) {
    return analysis.colors.skinTone;
  }
  
  // Legacy format
  if (analysis.skinTone) {
    return {
      primary: analysis.skinTone.undertone || 'unknown',
      hex: analysis.skinTone.hex,
      rgb: analysis.skinTone.rgb,
      confidence: 0.8
    };
  }
  
  return null;
};

export const getEyeColor = (analysis: FaceAnalysis): ColorData | null => {
  // New API format
  if (analysis.colors?.eyeColor) {
    return analysis.colors.eyeColor;
  }
  
  // Legacy format
  if (analysis.eyeColor) {
    return {
      primary: analysis.eyeColor.dominant || 'unknown',
      hex: analysis.eyeColor.hex,
      rgb: analysis.eyeColor.rgb,
      confidence: 0.8
    };
  }
  
  return null;
};

export const getHairColor = (analysis: FaceAnalysis): ColorData | null => {
  // New API format
  if (analysis.colors?.hairColor) {
    return analysis.colors.hairColor;
  }
  
  // Legacy format
  if (analysis.hairColor) {
    return {
      primary: analysis.hairColor.dominant || 'unknown',
      hex: analysis.hairColor.hex,
      rgb: analysis.hairColor.rgb,
      confidence: 0.8
    };
  }
  
  return null;
};

export const getLipColor = (analysis: FaceAnalysis): ColorData | null => {
  // New API format only
  if (analysis.colors?.lipColor) {
    return analysis.colors.lipColor;
  }
  
  return null;
};

export const getFaceShape = (analysis: FaceAnalysis): string => {
  // New API format
  if (analysis.features?.faceShape) {
    return analysis.features.faceShape;
  }
  
  // Legacy format
  if (analysis.faceShape) {
    return analysis.faceShape;
  }
  
  return 'Not detected';
};

export const getProcessingTime = (analysis: FaceAnalysis): number | null => {
  // New API format
  if (analysis.processingTime) {
    return analysis.processingTime;
  }
  
  // Legacy format
  if (analysis.analysisMetadata?.processingTime) {
    return analysis.analysisMetadata.processingTime;
  }
  
  return null;
};

export const getConfidence = (analysis: FaceAnalysis): number => {
  // New API format
  if (analysis.confidence) {
    return analysis.confidence;
  }
  
  // Legacy format
  if (analysis.analysisMetadata?.confidence) {
    return analysis.analysisMetadata.confidence;
  }
  
  return 0;
};

/**
 * Format color data for display
 */
export const formatColorDisplay = (colorData: ColorData | null): {
  name: string;
  hex: string | null;
  confidence: string;
} => {
  if (!colorData) {
    return {
      name: 'Not detected',
      hex: null,
      confidence: '0%'
    };
  }
  
  return {
    name: colorData.primary === 'unknown' ? 'Detected' : colorData.primary,
    hex: colorData.hex,
    confidence: colorData.confidence ? `${Math.round(colorData.confidence * 100)}%` : 'N/A'
  };
};

/**
 * Check if analysis has valid color data
 */
export const hasValidColorData = (analysis: FaceAnalysis): boolean => {
  const skinTone = getSkinTone(analysis);
  const eyeColor = getEyeColor(analysis);
  const hairColor = getHairColor(analysis);
  
  return !!(skinTone || eyeColor || hairColor);
};

/**
 * Get analysis summary for display
 */
export const getAnalysisSummary = (analysis: FaceAnalysis) => {
  return {
    skinTone: formatColorDisplay(getSkinTone(analysis)),
    eyeColor: formatColorDisplay(getEyeColor(analysis)),
    hairColor: formatColorDisplay(getHairColor(analysis)),
    lipColor: formatColorDisplay(getLipColor(analysis)),
    faceShape: getFaceShape(analysis),
    processingTime: getProcessingTime(analysis),
    confidence: getConfidence(analysis),
    hasValidData: hasValidColorData(analysis)
  };
};
